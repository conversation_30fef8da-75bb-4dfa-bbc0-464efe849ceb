import { Injectable, Logger } from '@nestjs/common';
import { KiteConnect } from 'kiteconnect';
import { EnvService } from '@app/core/env';
import { DateTimeUtilsService } from '@app/utils';
import { z } from 'zod/v4';
import {
  BrokerError,
  createKiteBrokerError,
  AuthenticationFailedError,
  InvalidCredentialsError,
  TokenExpiredError,
} from './broker.error';
import { ZERODHA_KITE_CONFIG, BROKER_DEFAULTS } from './broker.constants';
import { apiKeySchema, apiSecretSchema, accessTokenSchema } from './broker.schema';

// ==================== AUTHENTICATION SCHEMAS ====================

/**
 * Schema for generating Kite Connect login URL
 */
export const GenerateLoginUrlSchema = z
  .object({
    apiKey: apiKeySchema.describe('Zerodha Kite API key'),
    redirectUrl: z.string().url().optional().describe('Optional redirect URL after login'),
  })
  .strict();

/**
 * Schema for exchanging request token for access token
 */
export const GenerateAccessTokenSchema = z
  .object({
    apiKey: apiKeySchema.describe('Zerodha Kite API key'),
    apiSecret: apiSecretSchema.describe('Zerodha Kite API secret'),
    requestToken: z.string().min(1).max(255).describe('Request token from Kite Connect login'),
  })
  .strict();

/**
 * Schema for validating existing session
 */
export const ValidateSessionSchema = z
  .object({
    apiKey: apiKeySchema.describe('Zerodha Kite API key'),
    accessToken: accessTokenSchema.describe('Zerodha Kite access token'),
  })
  .strict();

/**
 * Schema for login URL response
 */
export const LoginUrlResponseSchema = z
  .object({
    loginUrl: z.string().url().describe('Kite Connect login URL'),
    apiKey: apiKeySchema.describe('API key used for login'),
    expiresAt: z.string().datetime().describe('URL expiration timestamp'),
  })
  .strict();

/**
 * Schema for access token response
 */
export const AccessTokenResponseSchema = z
  .object({
    accessToken: accessTokenSchema.describe('Generated access token'),
    userId: z.string().min(1).describe('Kite user ID'),
    userName: z.string().min(1).describe('Kite user name'),
    email: z.string().email().optional().describe('User email if available'),
    broker: z.literal('ZERODHA_KITE').describe('Broker type'),
    loginTime: z.string().datetime().describe('Login timestamp'),
    expiresAt: z.string().datetime().describe('Token expiration timestamp'),
  })
  .strict();

/**
 * Schema for session validation response
 */
export const SessionValidationResponseSchema = z
  .object({
    isValid: z.boolean().describe('Whether session is valid'),
    userId: z.string().min(1).optional().describe('Kite user ID if valid'),
    userName: z.string().min(1).optional().describe('Kite user name if valid'),
    email: z.string().email().optional().describe('User email if available'),
    loginTime: z.string().datetime().optional().describe('Login timestamp if valid'),
    expiresAt: z.string().datetime().optional().describe('Token expiration if valid'),
  })
  .strict();

// ==================== TYPE EXPORTS ====================

export type GenerateLoginUrlRequest = z.output<typeof GenerateLoginUrlSchema>;
export type GenerateAccessTokenRequest = z.output<typeof GenerateAccessTokenSchema>;
export type ValidateSessionRequest = z.output<typeof ValidateSessionSchema>;
export type LoginUrlResponse = z.output<typeof LoginUrlResponseSchema>;
export type AccessTokenResponse = z.output<typeof AccessTokenResponseSchema>;
export type SessionValidationResponse = z.output<typeof SessionValidationResponseSchema>;

// ==================== BROKER AUTHENTICATION SERVICE ====================

/**
 * Broker Authentication Service for Zerodha Kite Connect
 *
 * Provides authentication methods for Zerodha Kite Connect integration:
 * - Generate login URLs for user authentication
 * - Exchange request tokens for access tokens
 * - Validate existing sessions and tokens
 * - Handle Kite-specific authentication flows
 *
 * Features:
 * - Comprehensive error handling with domain-specific errors
 * - Structured logging for authentication events
 * - Zod schema validation for all inputs/outputs
 * - Session management integration
 * - Token expiration handling
 */
@Injectable()
export class BrokerAuthService {
  private readonly logger = new Logger(BrokerAuthService.name);

  constructor(
    private readonly envService: EnvService,
    private readonly dateTimeUtils: DateTimeUtilsService,
  ) {}

  // ==================== AUTHENTICATION METHODS ====================

  /**
   * Generate Kite Connect login URL for user authentication
   *
   * @param request - Login URL generation request
   * @returns Promise<LoginUrlResponse> - Generated login URL with metadata
   * @throws BrokerError if URL generation fails
   */
  async generateLoginUrl(request: GenerateLoginUrlRequest): Promise<LoginUrlResponse> {
    try {
      this.logger.log(`Generating Kite Connect login URL for API key: ${request.apiKey.substring(0, 8)}...`);

      // Validate input
      const validatedRequest = GenerateLoginUrlSchema.parse(request);

      // Create KiteConnect instance
      const kite = new KiteConnect({
        api_key: validatedRequest.apiKey,
      });

      // Generate login URL
      const loginUrl = kite.getLoginURL();
      const expiresAt = this.dateTimeUtils.addHours(this.dateTimeUtils.getUtcNow(), 1); // URLs typically expire in 1 hour

      const response: LoginUrlResponse = {
        loginUrl,
        apiKey: validatedRequest.apiKey,
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
      };

      this.logger.log(`Successfully generated Kite Connect login URL`);
      return LoginUrlResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to generate Kite Connect login URL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for login URL generation');
      }

      throw createKiteBrokerError(
        'AUTHENTICATION_FAILED',
        undefined,
        'Failed to generate Kite Connect login URL',
        error,
      );
    }
  }

  /**
   * Exchange request token for access token
   *
   * @param request - Access token generation request
   * @returns Promise<AccessTokenResponse> - Generated access token with user info
   * @throws BrokerError if token exchange fails
   */
  async generateAccessToken(request: GenerateAccessTokenRequest): Promise<AccessTokenResponse> {
    try {
      this.logger.log(`Exchanging request token for access token with API key: ${request.apiKey.substring(0, 8)}...`);

      // Validate input
      const validatedRequest = GenerateAccessTokenSchema.parse(request);

      // Create KiteConnect instance
      const kite = new KiteConnect({
        api_key: validatedRequest.apiKey,
      });

      // Exchange request token for access token
      const sessionData = await kite.generateSession(validatedRequest.requestToken, validatedRequest.apiSecret);

      // Set access token for profile fetch
      kite.setAccessToken(sessionData.access_token);

      // Fetch user profile for additional information
      const profile = await kite.getProfile();

      const loginTime = this.dateTimeUtils.getUtcNow();
      const expiresAt = this.dateTimeUtils.addHours(loginTime, 24); // Kite tokens typically expire in 24 hours

      const response: AccessTokenResponse = {
        accessToken: sessionData.access_token,
        userId: profile.user_id,
        userName: profile.user_name,
        email: profile.email || undefined,
        broker: 'ZERODHA_KITE',
        loginTime: this.dateTimeUtils.toUtcStorage(loginTime),
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
      };

      this.logger.log(`Successfully generated access token for user: ${profile.user_id}`);
      return AccessTokenResponseSchema.parse(response);
    } catch (error) {
      this.logger.error(
        `Failed to generate access token: ${error instanceof Error ? error.message : 'Unknown error'}`,
        error,
      );

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for access token generation');
      }

      // Handle Kite-specific errors
      if (error && typeof error === 'object' && 'error_type' in error) {
        const kiteError = error as { error_type: string; message: string };

        if (kiteError.error_type === 'TokenException') {
          throw new TokenExpiredError('ZERODHA_KITE');
        }

        if (kiteError.error_type === 'PermissionException') {
          throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid API credentials');
        }
      }

      throw createKiteBrokerError(
        'AUTHENTICATION_FAILED',
        undefined,
        'Failed to exchange request token for access token',
        error,
      );
    }
  }

  /**
   * Validate existing session and access token
   *
   * @param request - Session validation request
   * @returns Promise<SessionValidationResponse> - Session validation result
   * @throws BrokerError if validation fails
   */
  async validateSession(request: ValidateSessionRequest): Promise<SessionValidationResponse> {
    try {
      this.logger.log(`Validating session for API key: ${request.apiKey.substring(0, 8)}...`);

      // Validate input
      const validatedRequest = ValidateSessionSchema.parse(request);

      // Create KiteConnect instance
      const kite = new KiteConnect({
        api_key: validatedRequest.apiKey,
      });

      // Set access token
      kite.setAccessToken(validatedRequest.accessToken);

      // Validate session by fetching profile
      const profile = await kite.getProfile();

      const loginTime = this.dateTimeUtils.getUtcNow();
      const expiresAt = this.dateTimeUtils.addHours(loginTime, 24);

      const response: SessionValidationResponse = {
        isValid: true,
        userId: profile.user_id,
        userName: profile.user_name,
        email: profile.email || undefined,
        loginTime: this.dateTimeUtils.toUtcStorage(loginTime),
        expiresAt: this.dateTimeUtils.toUtcStorage(expiresAt),
      };

      this.logger.log(`Session validation successful for user: ${profile.user_id}`);
      return SessionValidationResponseSchema.parse(response);
    } catch (error) {
      this.logger.warn(`Session validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

      if (error instanceof z.ZodError) {
        throw new InvalidCredentialsError('ZERODHA_KITE', 'Invalid request parameters for session validation');
      }

      // Handle Kite-specific errors
      if (error && typeof error === 'object' && 'error_type' in error) {
        const kiteError = error as { error_type: string; message: string };

        if (kiteError.error_type === 'TokenException') {
          // Return invalid session instead of throwing error
          return SessionValidationResponseSchema.parse({
            isValid: false,
          });
        }
      }

      // For other errors, return invalid session
      return SessionValidationResponseSchema.parse({
        isValid: false,
      });
    }
  }
}
