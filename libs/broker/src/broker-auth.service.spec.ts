import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { BrokerAuthService } from './broker-auth.service';
import { EnvService } from '@app/core/env';
import { DateTimeUtilsService } from '@app/utils';
import { EncryptionService } from '@app/common/encryption';
import { BrokerRepository } from './broker.repository';

describe('BrokerAuthService', () => {
  let service: BrokerAuthService;
  let mockEnvService: jest.Mocked<EnvService>;
  let mockDateTimeUtils: jest.Mocked<DateTimeUtilsService>;
  let mockEncryptionService: jest.Mocked<EncryptionService>;
  let mockBrokerRepository: jest.Mocked<BrokerRepository>;

  beforeEach(async () => {
    // Create mocks
    mockEnvService = {
      get: jest.fn(),
    } as any;

    mockDateTimeUtils = {
      getUtcNow: jest.fn(),
      addHours: jest.fn(),
      toUtcStorage: jest.fn(),
    } as any;

    mockEncryptionService = {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    } as any;

    mockBrokerRepository = {
      findByUserIdAndType: jest.fn(),
      updateCredentials: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BrokerAuthService,
        { provide: EnvService, useValue: mockEnvService },
        { provide: DateTimeUtilsService, useValue: mockDateTimeUtils },
        { provide: EncryptionService, useValue: mockEncryptionService },
        { provide: BrokerRepository, useValue: mockBrokerRepository },
      ],
    }).compile();

    service = module.get<BrokerAuthService>(BrokerAuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBrokerCredentials', () => {
    it('should retrieve and decrypt broker credentials', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        apiKey: 'test-api-key',
        apiSecret: 'encrypted-api-secret',
        accessToken: 'encrypted-access-token',
        type: 'ZERODHA_KITE',
      };

      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.decrypt.mockReturnValue('decrypted-api-secret');

      // Act
      const result = await service.getBrokerCredentials({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toEqual({
        apiKey: 'test-api-key',
        apiSecret: 'decrypted-api-secret',
        brokerType: 'ZERODHA_KITE',
        hasAccessToken: true,
      });
      expect(mockBrokerRepository.findByUserIdAndType).toHaveBeenCalledWith(userId, 'ZERODHA_KITE');
      expect(mockEncryptionService.decrypt).toHaveBeenCalledWith('encrypted-api-secret');
    });

    it('should throw error when broker not found', async () => {
      // Arrange
      const userId = 'test-user-123';
      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.getBrokerCredentials({
          userId,
          brokerType: 'ZERODHA_KITE',
        }),
      ).rejects.toThrow();
    });
  });

  describe('storeAccessToken', () => {
    it('should encrypt and store access token', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        userId,
        type: 'ZERODHA_KITE',
      };

      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.encrypt.mockReturnValue('encrypted-access-token');
      mockBrokerRepository.updateCredentials.mockResolvedValue(mockBroker as any);

      // Act
      await service.storeAccessToken({
        userId,
        brokerType: 'ZERODHA_KITE',
        accessToken: 'test-access-token',
        brokerUserId: 'broker-user-123',
        brokerUserName: 'Test User',
        loginTime: '2024-01-01T10:00:00.000',
        expiresAt: '2024-01-02T10:00:00.000',
      });

      // Assert
      expect(mockEncryptionService.encrypt).toHaveBeenCalledWith('test-access-token');
      expect(mockBrokerRepository.updateCredentials).toHaveBeenCalledWith(1, userId, {
        accessToken: 'encrypted-access-token',
        status: 'ACTIVE',
      });
    });
  });

  describe('getStoredAccessToken', () => {
    it('should retrieve and decrypt stored access token', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockBroker = {
        id: 1,
        userId,
        name: 'Test Broker',
        accessToken: 'encrypted-access-token',
        lastConnectedAt: '2024-01-01T10:00:00.000',
      };

      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(mockBroker);
      mockEncryptionService.decrypt.mockReturnValue('decrypted-access-token');
      mockDateTimeUtils.getUtcNow.mockReturnValue('2024-01-01T12:00:00.000');
      mockDateTimeUtils.addHours.mockReturnValue('2024-01-02T10:00:00.000');
      mockDateTimeUtils.toUtcStorage.mockReturnValue('2024-01-02T10:00:00.000');

      // Act
      const result = await service.getStoredAccessToken({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toEqual({
        accessToken: 'decrypted-access-token',
        brokerUserId: userId,
        brokerUserName: 'Test Broker',
        email: undefined,
        loginTime: '2024-01-01T10:00:00.000',
        expiresAt: '2024-01-02T10:00:00.000',
        isValid: true,
      });
    });

    it('should return null when no stored token found', async () => {
      // Arrange
      const userId = 'test-user-123';
      mockBrokerRepository.findByUserIdAndType.mockResolvedValue(null);

      // Act
      const result = await service.getStoredAccessToken({
        userId,
        brokerType: 'ZERODHA_KITE',
      });

      // Assert
      expect(result).toBeNull();
    });
  });
});
